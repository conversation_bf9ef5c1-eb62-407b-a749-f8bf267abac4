package com.hmall.item;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@MapperScan("com.hmall.item.mapper")
@SpringBootApplication
@EnableDiscoveryClient
public class ItemApplication {
    public static void main(String[] args) {
        // 强制Nacos客户端使用HTTP协议而不是gRPC
        System.setProperty("nacos.remote.client.grpc.enable", "false");
        SpringApplication.run(ItemApplication.class, args);
    }
}
