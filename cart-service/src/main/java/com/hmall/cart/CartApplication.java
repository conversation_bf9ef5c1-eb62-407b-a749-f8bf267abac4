package com.hmall.cart;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@MapperScan("com.hmall.cart.mapper")
@SpringBootApplication
@EnableDiscoveryClient
public class CartApplication {
    public static void main(String[] args) {
        // 强制Nacos客户端使用HTTP协议而不是gRPC
        System.setProperty("nacos.remote.client.grpc.enable", "false");
        System.setProperty("com.alibaba.nacos.client.naming.tls.enable", "false");
        System.setProperty("com.alibaba.nacos.client.naming.load.cache.at.start", "false");
        SpringApplication.run(CartApplication.class, args);
    }
}
